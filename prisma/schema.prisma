// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model ChatSession {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  messages  Message[]
}

model Message {
  id          Int         @id @default(autoincrement())
  role        String      // "user", "assistant", or "tool"
  content     String
  createdAt   DateTime    @default(now())
  session     ChatSession @relation(fields: [sessionId], references: [id])
  sessionId   String
}