### **聊天机器人架构设计文档 (V3 - 最终版)**

#### **1. 系统概述**

本文档旨在设计一个基于 Remix 框架的智能聊天机器人。该系统后端集成 OpenAI API 用于语言模型推理和 Exa API 用于在线搜索，并使用 SQLite 数据库持久化存储对话历史。系统通过环境变量进行灵活配置，前端提供模型选择功能，无需复杂的后端用户系统。

#### **2. 核心技术栈**

*   **全栈框架**: Remix
*   **前端**: React
*   **后端运行时**: Node.js
*   **大语言模型**: OpenAI SDK (利用 **Tool Calling** 和 **流式响应** 功能)
*   **搜索引擎**: Exa API (作为 LLM 的一个 **工具**)
*   **数据库**: SQLite
*   **ORM**: Prisma
*   **校验**: Zod

#### **3. 系统架构 (最终)**

此版本引入了两个关键改进：**可插拔的工具架构** 和 **向前端发送结构化状态流**。

**最终架构图:**

```
+------------------+      (Structured JSON Stream)      +----------------------+
|                  |  <------------------------------->   |                      |
|   用户浏览器      |   1. Send Msg / 5. Receive Status   |     Remix Backend    |
| (React Frontend) |      & Final Streamed Response      | (Node.js / Server)   |
|                  |                                    |                      |
+------------------+                                    +----------------------+
                                                               |         ^
                                                               |         |
+--------------------------------------------------------------v---------+-------------------------+
| Agent Loop (在 Remix Backend 中执行)                                                              |
|                                                                                                 |
|   +-------------------------+   2. 请求回复(带工具定义)   +-----------------+   6. 发送工具结果   +-----------------+
|   |                         | ----------------------> |                 | ----------------> |                 |
|   |      Orchestrator       |                         |   OpenAI API    |                   |   OpenAI API    |
|   | (app/routes/_index.tsx) |   3. 返回工具调用请求     | (Tool Calling)  |   7. 返回最终回复(Stream) | (Summarization) |
|   |                         | <---------------------- |                 | <---------------- |                 |
|   +-------------------------+                         +-----------------+                   +-----------------+
|              |         |
|              |         | 4. 向前端发送Tool Call状态
|              |         v
|              |      (Frontend)
|              |
|              | 4. 查找并执行Tool
|              v
|   +-------------------------+
|   |                         |
|   | Tool Executor (toolMap) |
|   |                         |
|   +-------------------------+
|                                                                                                 |
+-------------------------------------------------------------------------------------------------+
```

#### **4. 关键实现细节**

##### **4.1. 可插拔的工具架构 (Pluggable Tools)**

为了方便未来扩展，我们将定义一个统一的工具接口。所有工具（如Exa搜索、计算器、数据库查询等）都将实现此接口。

*   **目录**: `app/tools/`
*   **基础接口 (`app/tools/base.ts`)**:
    ```typescript
    import { z } from 'zod';

    // 定义所有工具必须遵循的结构
    export interface Tool {
      // 工具名称，用于LLM识别
      name: string;
      // 工具功能的自然语言描述，供LLM理解何时使用
      description: string;
      // 定义工具输入参数的 Zod Schema，用于验证和类型提示
      schema: z.ZodObject<any, any, any>;
      // 执行工具的核心逻辑
      execute(args: z.infer<this['schema']>): Promise<string>;
    }
    ```
*   **工具实现 (`app/tools/exa_search.ts`)**:
    ```typescript
    import { Tool } from './base';
    import { z } from 'zod';
    import { search } from '../services/exa.server'; // 假设的Exa服务

    export class ExaSearchTool implements Tool {
      name = 'exa_search';
      description = '当需要回答关于最新事件或需要来自网络的信息时，使用此工具进行搜索。';
      schema = z.object({
        query: z.string().describe('The search query to find information for.'),
      });

      async execute({ query }: z.infer<typeof this.schema>): Promise<string> {
        const searchResults = await search(query);
        // 将搜索结果序列化为字符串，返回给LLM
        return JSON.stringify(searchResults);
      }
    }
    ```
*   **工具注册表 (`app/tools/index.ts`)**:
    ```typescript
    import { ExaSearchTool } from './exa_search';
    import type { Tool } from './base';

    // 导出一个所有可用工具的数组
    export const tools: Tool[] = [new ExaSearchTool()];

    // 创建一个Map，方便在Agent中按名称快速查找工具
    export const toolMap = new Map(tools.map(tool => [tool.name, tool]));
    ```
*   **Agent集成**: Agent编排器将导入 `tools` 数组，将其格式化后提供给OpenAI。当收到工具调用请求时，它会使用 `toolMap` 来查找并执行正确的工具。

##### **4.2. 前端状态更新与流式传输 (Structured Streaming)**

我们将不再向前端直接发送纯文本流，而是发送**结构化的JSON对象流**，每个对象都描述了其内容类型。

*   **数据流格式**:
    ```json
    // 当工具被调用时
    { "type": "tool_call", "name": "exa_search", "args": { "query": "AI news" } }

    // 当最终回复的文本块到达时
    { "type": "text_chunk", "content": "根据最新的搜索结果..." }

    // 当所有内容结束时
    { "type": "end" }
    ```
*   **后端实现 (`action` 函数)**:
    *   使用 `new Response(new ReadableStream(...))` 结合 `TransformStream` 来构建响应。
    *   Agent 逻辑的每一步都会向流中写入一个上述格式的JSON字符串。
        1.  当LLM决定调用工具时，写入 `{ "type": "tool_call", ... }`。
        2.  执行工具后，将结果发回给LLM。
        3.  当LLM开始生成最终回复时，将其响应流的每个 `chunk` 包装成 `{ "type": "text_chunk", ... }` 写入。
*   **前端实现 (`_index.tsx`)**:
    *   使用一个自定义Hook (`useChatStream`) 来处理 `fetcher.data` 返回的流。
    *   这个Hook会逐行读取流，`JSON.parse()`每一行。
    *   根据 `type` 字段更新React状态：
        *   `tool_call`: 设置一个 `toolStatus` 状态，例如 `{ name: 'exa_search' }`。UI会渲染 "正在使用Exa搜索..."。
        *   `text_chunk`: **首先清空 `toolStatus` 状态**，然后将 `content` 追加到主显示消息中。UI会隐藏工具状态，并开始流式显示最终答案。
        *   `end`: 标记流已结束，可以执行如启用输入框等最终操作。

#### **5. 最终数据流 (细化)**

1.  用户在浏览器输入 "最近有什么AI新闻?"，点击发送。
2.  前端 POST `{ message: "..." }` 到 `action`。
3.  后端Agent启动，将用户消息历史和 `tools` 定义发送给OpenAI，请求流式响应。
4.  OpenAI返回工具调用请求 `tool_calls: [{ name: 'exa_search', args: { query: 'AI news' } }]`。
5.  后端向客户端的响应流中写入: `{"type": "tool_call", "name": "exa_search", ...}`。
6.  **前端**: 接收到 `tool_call`，更新UI显示 "正在使用Exa搜索..."。
7.  后端使用 `toolMap` 找到 `ExaSearchTool` 并执行 `execute({ query: 'AI news' })`。
8.  后端将Exa的搜索结果作为新消息，再次调用OpenAI，请求流式响应。
9.  OpenAI开始流式返回总结后的文本。
10. 后端将收到的每个文本块包装后写入流: `{"type": "text_chunk", "content": "根据..."}`，`{"type": "text_chunk", "content": "最新的..."}`...
11. **前端**: 接收到第一个 `text_chunk`，**隐藏 "正在使用Exa搜索..."**，并开始将 `content` 渲染到屏幕上。后续的 `text_chunk` 会不断追加内容。
12. 流结束后，后端将完整的对话（用户问题、工具调用、工具结果、最终回答）存入SQLite。

#### **6. 最终目录结构**

```
/
├── app/
│   ├── components/
│   │   └── ...
│   ├── routes/
│   │   └── _index.tsx           # UI, Agent Action, Streaming Logic
│   ├── services/
│   │   ├── openai.server.ts
│   │   ├── exa.server.ts
│   │   └── db.server.ts
│   ├── tools/                   # <--- 新增: 可插拔工具
│   │   ├── base.ts              # Tool 接口
│   │   ├── exa_search.ts        # Exa 工具实现
│   │   └── index.ts             # 工具注册表
│   └── root.tsx
├── prisma/
│   ├── schema.prisma
│   └── dev.db
├── .env
├── package.json
└── ...
```
