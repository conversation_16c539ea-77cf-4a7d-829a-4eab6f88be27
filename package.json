{"name": "chat-bot", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@ai-sdk/openai": "^2.0.0", "@prisma/client": "^6.13.0", "@remix-run/node": "*", "@remix-run/react": "*", "@remix-run/serve": "*", "ai": "^5.0.0", "exa-js": "^1.8.26", "isbot": "^4.1.0", "openai": "^5.11.0", "prisma": "^6.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@remix-run/dev": "*", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}