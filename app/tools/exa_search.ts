import type { Tool } from './base';
import { z } from 'zod';
import { search } from '../services/exa.server';

export class ExaSearchTool implements Tool {
  name = 'exa_search';
  description = '当需要回答关于最新事件或需要来自网络的信息时，使用此工具进行搜索。';
  schema = z.object({
    query: z.string().describe('The search query to find information for.'),
  });

  async execute({ query }: z.infer<typeof this.schema>): Promise<string> {
    console.log(`Executing Exa search with query: "${query}"`);
    const searchResults = await search(query);
    // 将搜索结果序列化为字符串，返回给LLM
    return JSON.stringify(searchResults.results, null, 2);
  }
}
